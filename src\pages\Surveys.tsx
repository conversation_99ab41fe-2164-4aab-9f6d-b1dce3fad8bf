import React, { useState, useEffect } from 'react';
import { Plus, Calendar, Users, BarChart3, Play, Pause, Eye, Edit, Trash2 } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from '@/components/ui/alert-dialog';
import Layout from '@/components/Layout';
import SurveyForm from '@/components/forms/SurveyForm';
import { dataManager, SurveyData } from '@/utils/dataManager';

const Surveys = () => {
  const [surveys, setSurveys] = useState<SurveyData[]>([]);
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false);
  const [selectedSurvey, setSelectedSurvey] = useState<SurveyData | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    loadSurveys();
  }, []);

  const loadSurveys = () => {
    const data = dataManager.getSurveyData();
    setSurveys(data);
  };

  const handleAddSurvey = () => {
    setSelectedSurvey(null);
    setIsFormOpen(true);
  };

  const handleViewSurvey = (survey: SurveyData) => {
    setSelectedSurvey(survey);
    setIsViewDialogOpen(true);
  };

  const handleEditSurvey = (survey: SurveyData) => {
    setSelectedSurvey(survey);
    setIsFormOpen(true);
  };

  const handleDeleteSurvey = (survey: SurveyData) => {
    setSelectedSurvey(survey);
    setIsDeleteDialogOpen(true);
  };

  const handleFormSubmit = async (formData: Omit<SurveyData, 'id' | 'createdAt' | 'updatedAt'>) => {
    setIsLoading(true);
    try {
      if (selectedSurvey) {
        // Update existing survey
        dataManager.updateSurvey(selectedSurvey.id, formData);
      } else {
        // Add new survey
        dataManager.addSurvey(formData);
      }
      loadSurveys();
      setIsFormOpen(false);
      setSelectedSurvey(null);
    } catch (error) {
      console.error('Error saving survey:', error);
      alert('Gagal menyimpan data survey');
    } finally {
      setIsLoading(false);
    }
  };

  const confirmDelete = async () => {
    if (selectedSurvey) {
      setIsLoading(true);
      try {
        dataManager.deleteSurvey(selectedSurvey.id);
        loadSurveys();
        setIsDeleteDialogOpen(false);
        setSelectedSurvey(null);
      } catch (error) {
        console.error('Error deleting survey:', error);
        alert('Gagal menghapus data survey');
      } finally {
        setIsLoading(false);
      }
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge className="bg-green-100 text-green-800">Aktif</Badge>;
      case 'inactive':
        return <Badge className="bg-gray-100 text-gray-800">Tidak Aktif</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  const toggleSurveyStatus = (surveyId: string, currentStatus: string) => {
    const newStatus = currentStatus === 'active' ? 'inactive' : 'active';
    // Update survey status logic here
    console.log(`Toggle survey ${surveyId} to ${newStatus}`);
  };

  const getResponseCount = (survey: SurveyData) => {
    return survey.responses ? survey.responses.length : 0;
  };

  const getTargetCount = (survey: SurveyData) => {
    return survey.targetAlumni ? survey.targetAlumni.length : 0;
  };

  return (
    <Layout>
      <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Survey & Kuesioner</h1>
          <p className="text-gray-600">Kelola survey dan kuesioner untuk alumni</p>
        </div>
        <Button className="university-gradient" onClick={handleAddSurvey}>
          <Plus className="h-4 w-4 mr-2" />
          Buat Survey Baru
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <BarChart3 className="h-8 w-8 text-blue-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Survey</p>
                <p className="text-2xl font-bold text-gray-900">{surveys.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <Play className="h-8 w-8 text-green-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Survey Aktif</p>
                <p className="text-2xl font-bold text-gray-900">
                  {surveys.filter(s => s.status === 'active').length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <Users className="h-8 w-8 text-purple-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Responden</p>
                <p className="text-2xl font-bold text-gray-900">
                  {surveys.reduce((total, survey) => total + getResponseCount(survey), 0)}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <Calendar className="h-8 w-8 text-orange-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Survey Bulan Ini</p>
                <p className="text-2xl font-bold text-gray-900">
                  {surveys.filter(s => {
                    const surveyDate = new Date(s.tanggalMulai);
                    const currentDate = new Date();
                    return surveyDate.getMonth() === currentDate.getMonth() && 
                           surveyDate.getFullYear() === currentDate.getFullYear();
                  }).length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Survey List */}
      <Card>
        <CardHeader>
          <CardTitle>Daftar Survey</CardTitle>
        </CardHeader>
        <CardContent>
          {surveys.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <BarChart3 className="h-12 w-12 mx-auto mb-4 text-gray-300" />
              <p>Belum ada survey yang dibuat</p>
              <Button className="mt-4 university-gradient" onClick={handleAddSurvey}>
                <Plus className="h-4 w-4 mr-2" />
                Buat Survey Pertama
              </Button>
            </div>
          ) : (
            <div className="space-y-4">
              {surveys.map((survey) => (
                <div key={survey.id} className="border rounded-lg p-6 hover:bg-gray-50">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-2">
                        <h3 className="text-lg font-semibold text-gray-900">{survey.judul}</h3>
                        {getStatusBadge(survey.status)}
                      </div>
                      <p className="text-gray-600 mb-4">{survey.deskripsi}</p>
                      
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                        <div>
                          <span className="text-gray-500">Tanggal Mulai:</span>
                          <p className="font-medium">{new Date(survey.tanggalMulai).toLocaleDateString('id-ID')}</p>
                        </div>
                        <div>
                          <span className="text-gray-500">Tanggal Selesai:</span>
                          <p className="font-medium">{new Date(survey.tanggalSelesai).toLocaleDateString('id-ID')}</p>
                        </div>
                        <div>
                          <span className="text-gray-500">Target Alumni:</span>
                          <p className="font-medium">{getTargetCount(survey)} orang</p>
                        </div>
                        <div>
                          <span className="text-gray-500">Responden:</span>
                          <p className="font-medium">{getResponseCount(survey)} orang</p>
                        </div>
                      </div>
                      
                      <div className="mt-4">
                        <div className="flex items-center space-x-2 text-sm text-gray-600">
                          <span>Progress:</span>
                          <div className="flex-1 bg-gray-200 rounded-full h-2 max-w-xs">
                            <div 
                              className="bg-blue-600 h-2 rounded-full" 
                              style={{ 
                                width: `${getTargetCount(survey) > 0 ? (getResponseCount(survey) / getTargetCount(survey)) * 100 : 0}%` 
                              }}
                            ></div>
                          </div>
                          <span>
                            {getTargetCount(survey) > 0 ? 
                              Math.round((getResponseCount(survey) / getTargetCount(survey)) * 100) : 0}%
                          </span>
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex flex-col space-y-2 ml-4">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => toggleSurveyStatus(survey.id, survey.status)}
                      >
                        {survey.status === 'active' ? (
                          <>
                            <Pause className="h-4 w-4 mr-1" />
                            Pause
                          </>
                        ) : (
                          <>
                            <Play className="h-4 w-4 mr-1" />
                            Aktifkan
                          </>
                        )}
                      </Button>
                      
                      <div className="flex space-x-1">
                        <Button variant="ghost" size="sm" title="Lihat Detail" onClick={() => handleViewSurvey(survey)}>
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="sm" title="Edit" onClick={() => handleEditSurvey(survey)}>
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="text-red-600 hover:text-red-700"
                          title="Hapus"
                          onClick={() => handleDeleteSurvey(survey)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
      </div>

      {/* Form Dialog */}
      <Dialog open={isFormOpen} onOpenChange={setIsFormOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>{selectedSurvey ? 'Edit Survey' : 'Buat Survey Baru'}</DialogTitle>
          </DialogHeader>
          <SurveyForm
            survey={selectedSurvey}
            onSubmit={handleFormSubmit}
            onCancel={() => setIsFormOpen(false)}
            isLoading={isLoading}
          />
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Konfirmasi Hapus</AlertDialogTitle>
            <AlertDialogDescription>
              Apakah Anda yakin ingin menghapus survey <strong>{selectedSurvey?.title}</strong>?
              Semua data respons akan ikut terhapus. Tindakan ini tidak dapat dibatalkan.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isLoading}>Batal</AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmDelete}
              disabled={isLoading}
              className="bg-red-600 hover:bg-red-700"
            >
              {isLoading ? 'Menghapus...' : 'Hapus'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* View Survey Dialog */}
      <Dialog open={isViewDialogOpen} onOpenChange={setIsViewDialogOpen}>
        <DialogContent className="max-w-3xl">
          <DialogHeader>
            <DialogTitle>Detail Survey</DialogTitle>
          </DialogHeader>
          {selectedSurvey && (
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-700">Judul Survey</label>
                  <p className="mt-1 text-sm text-gray-900">{selectedSurvey.title}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">Status</label>
                  <p className="mt-1">
                    <Badge variant={selectedSurvey.status === 'active' ? 'default' : 'secondary'}>
                      {selectedSurvey.status === 'active' ? 'Aktif' : 'Tidak Aktif'}
                    </Badge>
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">Tanggal Mulai</label>
                  <p className="mt-1 text-sm text-gray-900">
                    {new Date(selectedSurvey.tanggalMulai).toLocaleDateString('id-ID')}
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">Tanggal Selesai</label>
                  <p className="mt-1 text-sm text-gray-900">
                    {new Date(selectedSurvey.tanggalSelesai).toLocaleDateString('id-ID')}
                  </p>
                </div>
                <div className="md:col-span-2">
                  <label className="text-sm font-medium text-gray-700">Deskripsi</label>
                  <p className="mt-1 text-sm text-gray-900">{selectedSurvey.description}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">Target Alumni</label>
                  <p className="mt-1 text-sm text-gray-900">{selectedSurvey.targetAlumni.length} alumni</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">Total Pertanyaan</label>
                  <p className="mt-1 text-sm text-gray-900">{selectedSurvey.questions.length} pertanyaan</p>
                </div>
              </div>

              {/* Questions Preview */}
              <div>
                <label className="text-sm font-medium text-gray-700 mb-3 block">Pertanyaan Survey</label>
                <div className="space-y-3 max-h-60 overflow-y-auto">
                  {selectedSurvey.questions.map((question, index) => (
                    <div key={question.id} className="p-3 border rounded-lg">
                      <div className="flex justify-between items-start mb-2">
                        <p className="text-sm font-medium">
                          {index + 1}. {question.question}
                        </p>
                        <Badge variant="outline" className="text-xs">
                          {question.type}
                        </Badge>
                      </div>
                      {question.options && question.options.length > 0 && (
                        <div className="mt-2">
                          <p className="text-xs text-gray-500 mb-1">Pilihan:</p>
                          <ul className="text-xs text-gray-600 list-disc list-inside">
                            {question.options.map((option, optIndex) => (
                              <li key={optIndex}>{option}</li>
                            ))}
                          </ul>
                        </div>
                      )}
                      {question.required && (
                        <Badge variant="destructive" className="text-xs mt-2">
                          Wajib
                        </Badge>
                      )}
                    </div>
                  ))}
                </div>
              </div>

              <div className="flex justify-end space-x-2">
                <Button variant="outline" onClick={() => setIsViewDialogOpen(false)}>
                  Tutup
                </Button>
                <Button onClick={() => {
                  setIsViewDialogOpen(false);
                  handleEditSurvey(selectedSurvey);
                }}>
                  Edit Survey
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </Layout>
  );
};

export default Surveys;
