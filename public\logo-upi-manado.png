data:image/png;base64,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
