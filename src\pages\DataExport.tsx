import React, { useState } from 'react';
import { Download, FileText, Table, Calendar, Filter, CheckCircle, Upload, FileSpreadsheet, Eye, Trash2, RefreshCw, Settings, Database, Users, Briefcase, ClipboardList } from 'lucide-react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import Layout from '@/components/Layout';
import { dataManager } from '@/utils/dataManager';

const DataExport = () => {
  const [selectedDataTypes, setSelectedDataTypes] = useState<string[]>(['alumni']);
  const [exportFormat, setExportFormat] = useState<string>('excel');
  const [dateRange, setDateRange] = useState({ start: '', end: '' });
  const [filters, setFilters] = useState({
    status: 'all',
    program: 'all',
    year: 'all'
  });
  const [isImportDialogOpen, setIsImportDialogOpen] = useState(false);
  const [isPreviewDialogOpen, setIsPreviewDialogOpen] = useState(false);
  const [isTemplateDialogOpen, setIsTemplateDialogOpen] = useState(false);
  const [importFile, setImportFile] = useState<File | null>(null);
  const [importProgress, setImportProgress] = useState(0);
  const [isImporting, setIsImporting] = useState(false);
  const [isExporting, setIsExporting] = useState(false);
  const [exportProgress, setExportProgress] = useState(0);
  const [previewData, setPreviewData] = useState<any[]>([]);
  const [importType, setImportType] = useState<string>('alumni');
  const [importOptions, setImportOptions] = useState({
    skipDuplicates: true,
    validateData: true,
    createBackup: true
  });

  const dataTypes = [
    {
      id: 'alumni',
      label: 'Data Alumni',
      description: 'Informasi lengkap alumni termasuk biodata dan akademik',
      icon: <Users className="h-4 w-4" />,
      fields: ['NIM', 'Nama Lengkap', 'Program Studi', 'Tahun Lulus', 'Email', 'Telepon', 'Alamat']
    },
    {
      id: 'employment',
      label: 'Data Pekerjaan',
      description: 'Informasi pekerjaan dan karir alumni',
      icon: <Briefcase className="h-4 w-4" />,
      fields: ['Nama Perusahaan', 'Posisi Jabatan', 'Status Pekerjaan', 'Gaji', 'Alamat Perusahaan']
    },
    {
      id: 'surveys',
      label: 'Data Survey',
      description: 'Hasil survey dan kuesioner alumni',
      icon: <ClipboardList className="h-4 w-4" />,
      fields: ['Judul Survey', 'Deskripsi', 'Tanggal Mulai', 'Tanggal Selesai', 'Status']
    },
    {
      id: 'responses',
      label: 'Respon Survey',
      description: 'Jawaban detail dari setiap survey',
      icon: <FileText className="h-4 w-4" />,
      fields: ['Survey ID', 'Alumni ID', 'Jawaban', 'Tanggal Respon']
    },
    {
      id: 'users',
      label: 'Data Users',
      description: 'Data pengguna sistem dan admin',
      icon: <Database className="h-4 w-4" />,
      fields: ['Username', 'Email', 'Role', 'Status', 'Last Login']
    },
    {
      id: 'settings',
      label: 'Pengaturan Sistem',
      description: 'Konfigurasi dan pengaturan aplikasi',
      icon: <Settings className="h-4 w-4" />,
      fields: ['Key', 'Value', 'Category', 'Description']
    }
  ];

  const exportFormats = [
    { value: 'excel', label: 'Excel (.xlsx)', icon: <Table className="h-4 w-4" /> },
    { value: 'csv', label: 'CSV (.csv)', icon: <FileText className="h-4 w-4" /> },
    { value: 'pdf', label: 'PDF (.pdf)', icon: <FileText className="h-4 w-4" /> },
    { value: 'json', label: 'JSON (.json)', icon: <FileText className="h-4 w-4" /> }
  ];

  const handleDataTypeChange = (dataType: string, checked: boolean) => {
    if (checked) {
      setSelectedDataTypes([...selectedDataTypes, dataType]);
    } else {
      setSelectedDataTypes(selectedDataTypes.filter(type => type !== dataType));
    }
  };

  // Export functionality
  const exportToCSV = (data: any[], filename: string, title?: string) => {
    if (data.length === 0) return;

    const headers = Object.keys(data[0]);
    let csvContent = '';

    // Add title if provided
    if (title) {
      csvContent += `${title}\n`;
      csvContent += `Exported on: ${new Date().toLocaleDateString('id-ID')}\n`;
      csvContent += `Total Records: ${data.length}\n\n`;
    }

    csvContent += [
      headers.join(','),
      ...data.map(row => headers.map(header => `"${row[header] || ''}"`).join(','))
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', filename);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const exportToExcel = (data: any[], filename: string, title?: string) => {
    if (data.length === 0) return;

    // Create Excel-like CSV with better formatting
    const headers = Object.keys(data[0]);
    let content = '';

    if (title) {
      content += `${title}\n`;
      content += `Exported on: ${new Date().toLocaleDateString('id-ID')}\n`;
      content += `Total Records: ${data.length}\n\n`;
    }

    // Add headers
    content += headers.join('\t') + '\n';

    // Add data rows
    data.forEach(row => {
      content += headers.map(header => row[header] || '').join('\t') + '\n';
    });

    const blob = new Blob([content], { type: 'application/vnd.ms-excel;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', filename.replace('.csv', '.xls'));
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const exportToPDF = (data: any[], filename: string, title?: string) => {
    if (data.length === 0) return;

    const headers = Object.keys(data[0]);
    let htmlContent = `
      <html>
        <head>
          <title>${title || 'Data Export'}</title>
          <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            h1 { color: #333; text-align: center; }
            .info { text-align: center; margin-bottom: 20px; color: #666; }
            table { width: 100%; border-collapse: collapse; margin-top: 20px; }
            th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
            th { background-color: #f2f2f2; font-weight: bold; }
            tr:nth-child(even) { background-color: #f9f9f9; }
          </style>
        </head>
        <body>
          <h1>${title || 'Data Export'}</h1>
          <div class="info">
            <p>Exported on: ${new Date().toLocaleDateString('id-ID')}</p>
            <p>Total Records: ${data.length}</p>
          </div>
          <table>
            <thead>
              <tr>${headers.map(h => `<th>${h}</th>`).join('')}</tr>
            </thead>
            <tbody>
              ${data.map(row =>
                `<tr>${headers.map(h => `<td>${row[h] || ''}</td>`).join('')}</tr>`
              ).join('')}
            </tbody>
          </table>
        </body>
      </html>
    `;

    const blob = new Blob([htmlContent], { type: 'text/html' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', filename.replace('.csv', '.html'));
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const exportToJSON = (data: any, filename: string) => {
    const exportData = {
      ...data,
      metadata: {
        exportDate: new Date().toISOString(),
        totalRecords: Object.values(data).reduce((total: number, arr: any) =>
          total + (Array.isArray(arr) ? arr.length : 0), 0),
        version: '1.0.0'
      }
    };

    const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', filename);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const handleExport = async () => {
    if (selectedDataTypes.length === 0) {
      alert('Pilih minimal satu jenis data untuk diekspor!');
      return;
    }

    setIsExporting(true);
    setExportProgress(0);

    try {
      const timestamp = new Date().toISOString().split('T')[0];

      // Get filtered data
      const data: any = {};

      if (selectedDataTypes.includes('alumni')) {
        data.alumni = dataManager.getAlumniData();
      }
      if (selectedDataTypes.includes('employment')) {
        data.employment = dataManager.getEmploymentData();
      }
      if (selectedDataTypes.includes('surveys')) {
        data.surveys = dataManager.getSurveyData();
      }
      if (selectedDataTypes.includes('responses')) {
        data.responses = dataManager.getSurveyResponses();
      }
      if (selectedDataTypes.includes('users')) {
        data.users = dataManager.getUserData();
      }
      if (selectedDataTypes.includes('settings')) {
        data.settings = dataManager.getSettingsData();
      }

      setExportProgress(25);

      // Apply filters if needed
      if (data.alumni && filters.year !== 'all') {
        data.alumni = data.alumni.filter((a: any) => a.tahunLulus?.toString() === filters.year);
      }
      if (data.alumni && filters.program !== 'all') {
        data.alumni = data.alumni.filter((a: any) => a.programStudi === filters.program);
      }
      if (data.alumni && filters.status !== 'all') {
        data.alumni = data.alumni.filter((a: any) => a.statusVerifikasi === filters.status);
      }

      // Apply date range filter
      if (dateRange.start && dateRange.end) {
        const startDate = new Date(dateRange.start);
        const endDate = new Date(dateRange.end);

        Object.keys(data).forEach(key => {
          if (Array.isArray(data[key])) {
            data[key] = data[key].filter((item: any) => {
              const itemDate = new Date(item.createdAt || item.tanggalDaftar || item.tanggal || new Date());
              return itemDate >= startDate && itemDate <= endDate;
            });
          }
        });
      }

      setExportProgress(50);

      const filename = `alumni_data_${timestamp}`;

      switch (exportFormat) {
        case 'json':
          exportToJSON(data, `${filename}.json`);
          break;
        case 'csv':
          // Export each type as separate CSV
          Object.keys(data).forEach(key => {
            if (Array.isArray(data[key]) && data[key].length > 0) {
              const title = dataTypes.find(dt => dt.id === key)?.label || key;
              exportToCSV(data[key], `${key}_${timestamp}.csv`, title);
            }
          });
          break;
        case 'excel':
          // Export as Excel format
          Object.keys(data).forEach(key => {
            if (Array.isArray(data[key]) && data[key].length > 0) {
              const title = dataTypes.find(dt => dt.id === key)?.label || key;
              exportToExcel(data[key], `${key}_${timestamp}.xls`, title);
            }
          });
          break;
        case 'pdf':
          // Export as PDF (HTML)
          Object.keys(data).forEach(key => {
            if (Array.isArray(data[key]) && data[key].length > 0) {
              const title = dataTypes.find(dt => dt.id === key)?.label || key;
              exportToPDF(data[key], `${key}_${timestamp}.html`, title);
            }
          });
          break;
      }

      setExportProgress(100);

      setTimeout(() => {
        setIsExporting(false);
        setExportProgress(0);
        alert(`Export ${exportFormat.toUpperCase()} berhasil! ${Object.keys(data).length} jenis data telah diekspor.`);
      }, 500);

    } catch (error) {
      console.error('Export error:', error);
      setIsExporting(false);
      setExportProgress(0);
      alert('Error saat export data!');
    }
  };

  // Preview functionality
  const handlePreviewData = (dataType: string) => {
    let data: any[] = [];

    switch (dataType) {
      case 'alumni':
        data = dataManager.getAlumniData().slice(0, 10); // Preview first 10 records
        break;
      case 'employment':
        data = dataManager.getEmploymentData().slice(0, 10);
        break;
      case 'surveys':
        data = dataManager.getSurveyData().slice(0, 10);
        break;
      case 'responses':
        data = dataManager.getSurveyResponses().slice(0, 10);
        break;
      case 'users':
        data = dataManager.getUserData().slice(0, 10);
        break;
      case 'settings':
        data = dataManager.getSettingsData().slice(0, 10);
        break;
    }

    setPreviewData(data);
    setIsPreviewDialogOpen(true);
  };

  // Template download functionality
  const downloadTemplate = (dataType: string) => {
    const templates: any = {
      alumni: [
        {
          nim: '12345678',
          namaLengkap: 'John Doe',
          programStudi: 'Teknik Informatika',
          tahunLulus: '2023',
          email: '<EMAIL>',
          telepon: '081234567890',
          alamat: 'Jl. Contoh No. 123'
        }
      ],
      employment: [
        {
          alumniId: '12345678',
          namaPerusahaan: 'PT. Contoh',
          posisiJabatan: 'Software Developer',
          statusPekerjaan: 'bekerja',
          gaji: '8000000',
          alamatPerusahaan: 'Jl. Perusahaan No. 456'
        }
      ],
      surveys: [
        {
          judul: 'Survey Kepuasan Alumni',
          deskripsi: 'Survey untuk mengukur kepuasan alumni',
          tanggalMulai: '2024-01-01',
          tanggalSelesai: '2024-12-31',
          status: 'aktif'
        }
      ]
    };

    const template = templates[dataType] || [];
    const filename = `template_${dataType}_${new Date().toISOString().split('T')[0]}.csv`;

    if (template.length > 0) {
      exportToCSV(template, filename, `Template ${dataType.toUpperCase()}`);
    } else {
      alert('Template tidak tersedia untuk jenis data ini');
    }
  };

  // Data validation
  const validateImportData = (data: any[], dataType: string): { valid: boolean; errors: string[] } => {
    const errors: string[] = [];

    if (!Array.isArray(data) || data.length === 0) {
      errors.push('Data kosong atau format tidak valid');
      return { valid: false, errors };
    }

    const requiredFields: any = {
      alumni: ['namaLengkap', 'nim'],
      employment: ['namaPerusahaan', 'posisiJabatan'],
      surveys: ['judul', 'deskripsi']
    };

    const required = requiredFields[dataType] || [];

    data.forEach((item, index) => {
      required.forEach((field: string) => {
        if (!item[field] || item[field].toString().trim() === '') {
          errors.push(`Baris ${index + 1}: Field '${field}' wajib diisi`);
        }
      });
    });

    return { valid: errors.length === 0, errors };
  };

  // Import functionality
  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setImportFile(file);
    }
  };

  const handleImport = async () => {
    if (!importFile) {
      alert('Pilih file terlebih dahulu!');
      return;
    }

    setIsImporting(true);
    setImportProgress(0);

    try {
      // Create backup if option is enabled
      if (importOptions.createBackup) {
        const backupData = {
          alumni: dataManager.getAlumniData(),
          employment: dataManager.getEmploymentData(),
          surveys: dataManager.getSurveyData(),
          users: dataManager.getUserData(),
          settings: dataManager.getSettingsData()
        };

        const backupFilename = `backup_before_import_${new Date().toISOString().split('T')[0]}.json`;
        exportToJSON(backupData, backupFilename);
      }

      setImportProgress(10);

      const reader = new FileReader();
      reader.onload = async (e) => {
        try {
          const content = e.target?.result as string;
          setImportProgress(25);

          let importData: any[] = [];
          let importCount = 0;
          let skipCount = 0;
          let errorCount = 0;

          if (importFile.name.endsWith('.json')) {
            const data = JSON.parse(content);
            setImportProgress(40);

            // Handle different JSON structures
            if (data[importType] && Array.isArray(data[importType])) {
              importData = data[importType];
            } else if (Array.isArray(data)) {
              importData = data;
            } else {
              throw new Error('Format JSON tidak valid');
            }

          } else if (importFile.name.endsWith('.csv')) {
            // Parse CSV
            const lines = content.split('\n').filter(line => line.trim());
            if (lines.length < 2) {
              throw new Error('File CSV kosong atau tidak valid');
            }

            const headers = lines[0].split(',').map(h => h.replace(/"/g, '').trim());
            setImportProgress(40);

            for (let i = 1; i < lines.length; i++) {
              const values = lines[i].split(',').map(v => v.replace(/"/g, '').trim());
              const record: any = {};

              headers.forEach((header, index) => {
                record[header] = values[index] || '';
              });

              importData.push(record);
            }
          }

          setImportProgress(60);

          // Validate data if option is enabled
          if (importOptions.validateData) {
            const validation = validateImportData(importData, importType);
            if (!validation.valid) {
              const proceed = confirm(`Ditemukan ${validation.errors.length} error:\n${validation.errors.slice(0, 5).join('\n')}\n\nLanjutkan import?`);
              if (!proceed) {
                setIsImporting(false);
                setImportProgress(0);
                return;
              }
            }
          }

          setImportProgress(80);

          // Import data based on type
          for (const item of importData) {
            try {
              let shouldSkip = false;

              // Check for duplicates if option is enabled
              if (importOptions.skipDuplicates) {
                const existing = getExistingRecord(item, importType);
                if (existing) {
                  skipCount++;
                  shouldSkip = true;
                }
              }

              if (!shouldSkip) {
                switch (importType) {
                  case 'alumni':
                    dataManager.addAlumni({ ...item, id: Date.now() + Math.random() });
                    break;
                  case 'employment':
                    dataManager.addEmployment({ ...item, id: Date.now() + Math.random() });
                    break;
                  case 'surveys':
                    dataManager.addSurvey({ ...item, id: Date.now() + Math.random() });
                    break;
                  case 'users':
                    dataManager.addUser({ ...item, id: Date.now() + Math.random() });
                    break;
                }
                importCount++;
              }
            } catch (error) {
              errorCount++;
              console.error('Error importing item:', error);
            }
          }

          setImportProgress(100);

          setTimeout(() => {
            setIsImporting(false);
            setIsImportDialogOpen(false);
            setImportFile(null);
            setImportProgress(0);

            const message = `Import selesai!\n\nBerhasil: ${importCount} record\nDilewati: ${skipCount} record\nError: ${errorCount} record`;
            alert(message);
          }, 500);

        } catch (error) {
          console.error('Import error:', error);
          setIsImporting(false);
          setImportProgress(0);
          alert('Error saat import file. Pastikan format file benar.');
        }
      };

      reader.readAsText(importFile);

    } catch (error) {
      console.error('Import error:', error);
      setIsImporting(false);
      setImportProgress(0);
      alert('Error saat memproses import.');
    }
  };

  // Helper function to check existing records
  const getExistingRecord = (item: any, dataType: string) => {
    switch (dataType) {
      case 'alumni':
        return dataManager.getAlumniData().find(a => a.nim === item.nim || a.email === item.email);
      case 'employment':
        return dataManager.getEmploymentData().find(e => e.alumniId === item.alumniId);
      case 'surveys':
        return dataManager.getSurveyData().find(s => s.judul === item.judul);
      case 'users':
        return dataManager.getUserData().find(u => u.username === item.username || u.email === item.email);
      default:
        return null;
    }
  };

  const getDataCount = (dataType: string) => {
    switch (dataType) {
      case 'alumni':
        return dataManager.getAlumniData().length;
      case 'employment':
        return dataManager.getEmploymentData().length;
      case 'surveys':
        return dataManager.getSurveyData().length;
      case 'responses':
        return dataManager.getSurveyData().reduce((total, survey) => total + (survey.responses?.length || 0), 0);
      default:
        return 0;
    }
  };

  return (
    <Layout>
      <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Data Export & Import</h1>
          <p className="text-gray-600">Export dan import data alumni dalam berbagai format</p>
        </div>
        <div className="flex space-x-2">
          <Dialog open={isTemplateDialogOpen} onOpenChange={setIsTemplateDialogOpen}>
            <DialogTrigger asChild>
              <Button variant="outline">
                <FileSpreadsheet className="h-4 w-4 mr-2" />
                Download Template
              </Button>
            </DialogTrigger>
          </Dialog>

          <Dialog open={isImportDialogOpen} onOpenChange={setIsImportDialogOpen}>
            <DialogTrigger asChild>
              <Button variant="outline">
                <Upload className="h-4 w-4 mr-2" />
                Import Data
              </Button>
            </DialogTrigger>
          </Dialog>

          <Button
            className="university-gradient"
            onClick={handleExport}
            disabled={selectedDataTypes.length === 0 || isExporting}
          >
            {isExporting ? (
              <>
                <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                Exporting...
              </>
            ) : (
              <>
                <Download className="h-4 w-4 mr-2" />
                Export Sekarang
              </>
            )}
          </Button>
        </div>
      </div>

      {/* Export Progress */}
      {isExporting && (
        <Card>
          <CardContent className="pt-6">
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Mengekspor data...</span>
                <span>{exportProgress}%</span>
              </div>
              <Progress value={exportProgress} className="w-full" />
            </div>
          </CardContent>
        </Card>
      )}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Export Configuration */}
        <div className="lg:col-span-2 space-y-6">
          {/* Data Selection */}
          <Card>
            <CardHeader>
              <CardTitle>Pilih Data yang Akan Diekspor</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {dataTypes.map((dataType) => (
                <div key={dataType.id} className="flex items-start space-x-3 p-3 border rounded-lg hover:bg-gray-50">
                  <Checkbox
                    id={dataType.id}
                    checked={selectedDataTypes.includes(dataType.id)}
                    onCheckedChange={(checked) => handleDataTypeChange(dataType.id, checked as boolean)}
                  />
                  <div className="flex-1">
                    <div className="flex items-center space-x-2">
                      {dataType.icon}
                      <label htmlFor={dataType.id} className="text-sm font-medium cursor-pointer">
                        {dataType.label}
                      </label>
                    </div>
                    <p className="text-xs text-gray-500 mt-1">{dataType.description}</p>
                    <div className="flex items-center justify-between mt-2">
                      <Badge variant="secondary" className="text-xs">
                        {getDataCount(dataType.id)} record
                      </Badge>
                      <div className="flex space-x-1">
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={() => handlePreviewData(dataType.id)}
                          className="h-6 px-2 text-xs"
                        >
                          <Eye className="h-3 w-3 mr-1" />
                          Preview
                        </Button>
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={() => downloadTemplate(dataType.id)}
                          className="h-6 px-2 text-xs"
                        >
                          <Download className="h-3 w-3 mr-1" />
                          Template
                        </Button>
                      </div>
                    </div>
                    {dataType.fields && (
                      <div className="mt-2">
                        <p className="text-xs text-gray-400">Fields: {dataType.fields.join(', ')}</p>
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>

          {/* Format Selection */}
          <Card>
            <CardHeader>
              <CardTitle>Format Export</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-3">
                {exportFormats.map((format) => (
                  <div
                    key={format.value}
                    className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                      exportFormat === format.value
                        ? 'border-blue-500 bg-blue-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                    onClick={() => setExportFormat(format.value)}
                  >
                    <div className="flex items-center space-x-2">
                      {format.icon}
                      <span className="text-sm font-medium">{format.label}</span>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Filters */}
          <Card>
            <CardHeader>
              <CardTitle>Filter Data</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-700 mb-2 block">Tanggal Mulai</label>
                  <Input
                    type="date"
                    value={dateRange.start}
                    onChange={(e) => setDateRange({ ...dateRange, start: e.target.value })}
                  />
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700 mb-2 block">Tanggal Selesai</label>
                  <Input
                    type="date"
                    value={dateRange.end}
                    onChange={(e) => setDateRange({ ...dateRange, end: e.target.value })}
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-700 mb-2 block">Status Verifikasi</label>
                  <Select value={filters.status} onValueChange={(value) => setFilters({ ...filters, status: value })}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">Semua Status</SelectItem>
                      <SelectItem value="verified">Terverifikasi</SelectItem>
                      <SelectItem value="pending">Menunggu</SelectItem>
                      <SelectItem value="rejected">Ditolak</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <label className="text-sm font-medium text-gray-700 mb-2 block">Program Studi</label>
                  <Select value={filters.program} onValueChange={(value) => setFilters({ ...filters, program: value })}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">Semua Program</SelectItem>
                      <SelectItem value="Teknik Informatika">Teknik Informatika</SelectItem>
                      <SelectItem value="Sistem Informasi">Sistem Informasi</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <label className="text-sm font-medium text-gray-700 mb-2 block">Tahun Lulus</label>
                  <Select value={filters.year} onValueChange={(value) => setFilters({ ...filters, year: value })}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">Semua Tahun</SelectItem>
                      <SelectItem value="2024">2024</SelectItem>
                      <SelectItem value="2023">2023</SelectItem>
                      <SelectItem value="2022">2022</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Export Summary */}
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Ringkasan Export</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h4 className="text-sm font-medium text-gray-700 mb-2">Data yang Dipilih:</h4>
                <div className="space-y-1">
                  {selectedDataTypes.map((type) => {
                    const dataType = dataTypes.find(dt => dt.id === type);
                    return (
                      <div key={type} className="flex items-center space-x-2 text-sm">
                        <CheckCircle className="h-4 w-4 text-green-600" />
                        <span>{dataType?.label}</span>
                        <span className="text-gray-500">({getDataCount(type)})</span>
                      </div>
                    );
                  })}
                </div>
              </div>

              <div>
                <h4 className="text-sm font-medium text-gray-700 mb-2">Format:</h4>
                <p className="text-sm text-gray-600">
                  {exportFormats.find(f => f.value === exportFormat)?.label}
                </p>
              </div>

              <div>
                <h4 className="text-sm font-medium text-gray-700 mb-2">Total Record:</h4>
                <p className="text-lg font-bold text-blue-600">
                  {selectedDataTypes.reduce((total, type) => total + getDataCount(type), 0)}
                </p>
              </div>

              <Button 
                className="w-full university-gradient" 
                onClick={handleExport}
                disabled={selectedDataTypes.length === 0}
              >
                <Download className="h-4 w-4 mr-2" />
                Export Data
              </Button>
            </CardContent>
          </Card>

          {/* Quick Export */}
          <Card>
            <CardHeader>
              <CardTitle>Quick Export</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <Button variant="outline" className="w-full justify-start" onClick={() => {
                setSelectedDataTypes(['alumni']);
                setExportFormat('excel');
                handleExport();
              }}>
                <Table className="h-4 w-4 mr-2" />
                Export Alumni (Excel)
              </Button>
              <Button variant="outline" className="w-full justify-start" onClick={() => {
                setSelectedDataTypes(['employment']);
                setExportFormat('csv');
                handleExport();
              }}>
                <FileText className="h-4 w-4 mr-2" />
                Export Pekerjaan (CSV)
              </Button>
              <Button variant="outline" className="w-full justify-start" onClick={() => {
                setSelectedDataTypes(['surveys', 'responses']);
                setExportFormat('json');
                handleExport();
              }}>
                <FileText className="h-4 w-4 mr-2" />
                Export Survey (JSON)
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Preview Data Dialog */}
      <Dialog open={isPreviewDialogOpen} onOpenChange={setIsPreviewDialogOpen}>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-auto">
          <DialogHeader>
            <DialogTitle>Preview Data</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            {previewData.length > 0 ? (
              <div className="overflow-x-auto">
                <table className="w-full border-collapse border border-gray-300">
                  <thead>
                    <tr className="bg-gray-50">
                      {Object.keys(previewData[0]).map((key) => (
                        <th key={key} className="border border-gray-300 px-3 py-2 text-left text-sm font-medium">
                          {key}
                        </th>
                      ))}
                    </tr>
                  </thead>
                  <tbody>
                    {previewData.map((row, index) => (
                      <tr key={index} className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
                        {Object.values(row).map((value: any, cellIndex) => (
                          <td key={cellIndex} className="border border-gray-300 px-3 py-2 text-sm">
                            {value?.toString() || '-'}
                          </td>
                        ))}
                      </tr>
                    ))}
                  </tbody>
                </table>
                <p className="text-sm text-gray-500 mt-2">
                  Menampilkan {previewData.length} dari total data (maksimal 10 record untuk preview)
                </p>
              </div>
            ) : (
              <div className="text-center py-8 text-gray-500">
                <Database className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>Tidak ada data untuk ditampilkan</p>
              </div>
            )}
          </div>
        </DialogContent>
      </Dialog>

      {/* Template Download Dialog */}
      <Dialog open={isTemplateDialogOpen} onOpenChange={setIsTemplateDialogOpen}>
        <DialogContent className="max-w-lg">
          <DialogHeader>
            <DialogTitle>Download Template</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <p className="text-sm text-gray-600">
              Pilih template yang ingin didownload untuk memudahkan proses import data:
            </p>

            <div className="space-y-3">
              {dataTypes.map((dataType) => (
                <Button
                  key={dataType.id}
                  variant="outline"
                  className="w-full justify-start"
                  onClick={() => {
                    downloadTemplate(dataType.id);
                    setIsTemplateDialogOpen(false);
                  }}
                >
                  {dataType.icon}
                  <span className="ml-2">Template {dataType.label}</span>
                  <Badge variant="secondary" className="ml-auto">
                    {dataType.fields?.length || 0} fields
                  </Badge>
                </Button>
              ))}
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Import Dialog */}
      <Dialog open={isImportDialogOpen} onOpenChange={setIsImportDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Import Data</DialogTitle>
          </DialogHeader>

          <div className="space-y-6">
            {/* Import Type Selection */}
            <div>
              <Label className="text-sm font-medium mb-2 block">Jenis Data yang Akan Diimport:</Label>
              <Select value={importType} onValueChange={setImportType}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {dataTypes.map((dataType) => (
                    <SelectItem key={dataType.id} value={dataType.id}>
                      <div className="flex items-center space-x-2">
                        {dataType.icon}
                        <span>{dataType.label}</span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Import Options */}
            <div className="space-y-3">
              <Label className="text-sm font-medium">Opsi Import:</Label>
              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="skipDuplicates"
                    checked={importOptions.skipDuplicates}
                    onCheckedChange={(checked) =>
                      setImportOptions({...importOptions, skipDuplicates: checked as boolean})
                    }
                  />
                  <Label htmlFor="skipDuplicates" className="text-sm">
                    Lewati data duplikat
                  </Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="validateData"
                    checked={importOptions.validateData}
                    onCheckedChange={(checked) =>
                      setImportOptions({...importOptions, validateData: checked as boolean})
                    }
                  />
                  <Label htmlFor="validateData" className="text-sm">
                    Validasi data sebelum import
                  </Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="createBackup"
                    checked={importOptions.createBackup}
                    onCheckedChange={(checked) =>
                      setImportOptions({...importOptions, createBackup: checked as boolean})
                    }
                  />
                  <Label htmlFor="createBackup" className="text-sm">
                    Buat backup sebelum import
                  </Label>
                </div>
              </div>
            </div>

            {/* File Upload */}
            <div>
              <Label className="text-sm font-medium mb-2 block">Pilih File untuk Import:</Label>
              <Input
                type="file"
                accept=".json,.csv,.xlsx"
                onChange={handleFileUpload}
                disabled={isImporting}
              />
              <p className="text-xs text-gray-500 mt-1">
                Format yang didukung: JSON, CSV, Excel (maksimal 10MB)
              </p>
            </div>

            {/* File Info */}
            {importFile && (
              <div className="p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center space-x-2">
                  <FileSpreadsheet className="h-4 w-4 text-blue-600" />
                  <div>
                    <p className="text-sm font-medium">{importFile.name}</p>
                    <p className="text-xs text-gray-500">
                      Ukuran: {(importFile.size / 1024).toFixed(2)} KB
                    </p>
                  </div>
                </div>
              </div>
            )}

            {/* Import Progress */}
            {isImporting && (
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Progress Import:</span>
                  <span>{importProgress}%</span>
                </div>
                <Progress value={importProgress} className="w-full" />
              </div>
            )}

            {/* Action Buttons */}
            <div className="flex space-x-2">
              <Button
                variant="outline"
                onClick={() => setIsImportDialogOpen(false)}
                className="flex-1"
                disabled={isImporting}
              >
                Batal
              </Button>
              <Button
                onClick={handleImport}
                disabled={!importFile || isImporting}
                className="flex-1 university-gradient"
              >
                {isImporting ? (
                  <>
                    <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                    Importing...
                  </>
                ) : (
                  <>
                    <Upload className="h-4 w-4 mr-2" />
                    Import Data
                  </>
                )}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </Layout>
  );
};

export default DataExport;
