import React, { useState, useEffect } from 'react';
import { BarChart3, TrendingUp, Users, Download, Calendar, Filter, Upload, FileText, Table, FileSpreadsheet, Building } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, PieChart, Pie, Cell, LineChart, Line } from 'recharts';
import Layout from '@/components/Layout';
import { dataManager, AlumniData, EmploymentData, SurveyData } from '@/utils/dataManager';

const Reports = () => {
  const [alumni, setAlumni] = useState<AlumniData[]>([]);
  const [employment, setEmployment] = useState<EmploymentData[]>([]);
  const [surveys, setSurveys] = useState<SurveyData[]>([]);
  const [selectedYear, setSelectedYear] = useState<string>('all');
  const [selectedProgram, setSelectedProgram] = useState<string>('all');
  const [isExportDialogOpen, setIsExportDialogOpen] = useState(false);
  const [isImportDialogOpen, setIsImportDialogOpen] = useState(false);
  const [selectedExportTypes, setSelectedExportTypes] = useState<string[]>(['alumni']);
  const [exportFormat, setExportFormat] = useState<string>('excel');
  const [importFile, setImportFile] = useState<File | null>(null);

  useEffect(() => {
    loadData();
  }, []);

  const loadData = () => {
    setAlumni(dataManager.getAlumniData());
    setEmployment(dataManager.getEmploymentData());
    setSurveys(dataManager.getSurveyData());
  };

  // Filter data based on selected filters
  const filteredAlumni = alumni.filter(a => {
    const yearMatch = selectedYear === 'all' || a.tahunLulus.toString() === selectedYear;
    const programMatch = selectedProgram === 'all' || a.programStudi === selectedProgram;
    return yearMatch && programMatch;
  });

  // Get unique years and programs for filters
  const years = [...new Set(alumni.map(a => a.tahunLulus))].sort((a, b) => b - a);
  const programs = [...new Set(alumni.map(a => a.programStudi))];

  // Employment status data for pie chart
  const employmentStatusData = [
    { name: 'Bekerja', value: employment.filter(e => e.statusPekerjaan === 'bekerja').length, color: '#10B981' },
    { name: 'Tidak Bekerja', value: employment.filter(e => e.statusPekerjaan === 'tidak_bekerja').length, color: '#EF4444' },
    { name: 'Wirausaha', value: employment.filter(e => e.statusPekerjaan === 'wirausaha').length, color: '#F59E0B' }
  ];

  // Alumni by year data for bar chart
  const alumniByYearData = years.map(year => ({
    year: year.toString(),
    count: alumni.filter(a => a.tahunLulus === year).length
  }));

  // Average IPK by program
  const ipkByProgramData = programs.map(program => {
    const programAlumni = filteredAlumni.filter(a => a.programStudi === program);
    const avgIpk = programAlumni.length > 0 
      ? programAlumni.reduce((sum, a) => sum + a.ipk, 0) / programAlumni.length 
      : 0;
    return {
      program: program.length > 15 ? program.substring(0, 15) + '...' : program,
      ipk: parseFloat(avgIpk.toFixed(2))
    };
  });

  // Salary trend data
  const salaryTrendData = employment.map((emp, index) => ({
    name: `Alumni ${index + 1}`,
    gajiPertama: emp.gajiPertama / 1000000,
    gajiSaatIni: emp.gajiSaatIni / 1000000
  }));

  // Export functionality
  const exportToCSV = (data: any[], filename: string) => {
    if (data.length === 0) return;

    const headers = Object.keys(data[0]);
    const csvContent = [
      headers.join(','),
      ...data.map(row => headers.map(header => `"${row[header] || ''}"`).join(','))
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', filename);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const exportToJSON = (data: any, filename: string) => {
    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', filename);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const exportToExcel = (data: any[], filename: string) => {
    // For now, export as CSV (can be enhanced with actual Excel library)
    exportToCSV(data, filename.replace('.xlsx', '.csv'));
  };

  const exportReport = (type: string) => {
    const timestamp = new Date().toISOString().split('T')[0];

    switch (type) {
      case 'alumni-by-year':
        exportToCSV(alumniByYearData, `alumni_by_year_${timestamp}.csv`);
        break;
      case 'alumni-by-program':
        exportToCSV(alumniByProgramData, `alumni_by_program_${timestamp}.csv`);
        break;
      case 'employment-status':
        exportToCSV(employmentStatusData, `employment_status_${timestamp}.csv`);
        break;
      case 'ipk-by-program':
        exportToCSV(ipkByProgramData, `ipk_by_program_${timestamp}.csv`);
        break;
      case 'salary-trend':
        exportToCSV(salaryTrendData, `salary_trend_${timestamp}.csv`);
        break;
      case 'all-alumni':
        exportToCSV(filteredAlumni, `all_alumni_${timestamp}.csv`);
        break;
      case 'all-employment':
        exportToCSV(employment, `all_employment_${timestamp}.csv`);
        break;
      case 'all-surveys':
        exportToCSV(surveys, `all_surveys_${timestamp}.csv`);
        break;
      default:
        console.log(`Exporting ${type} report...`);
    }
  };

  const handleBulkExport = () => {
    const timestamp = new Date().toISOString().split('T')[0];
    const exportData: any = {};

    if (selectedExportTypes.includes('alumni')) {
      exportData.alumni = filteredAlumni;
    }
    if (selectedExportTypes.includes('employment')) {
      exportData.employment = employment;
    }
    if (selectedExportTypes.includes('surveys')) {
      exportData.surveys = surveys;
    }
    if (selectedExportTypes.includes('charts')) {
      exportData.charts = {
        alumniByYear: alumniByYearData,
        alumniByProgram: alumniByProgramData,
        ipkByProgram: ipkByProgramData,
        salaryTrend: salaryTrendData
      };
    }

    const filename = `bulk_export_${timestamp}`;

    switch (exportFormat) {
      case 'json':
        exportToJSON(exportData, `${filename}.json`);
        break;
      case 'csv':
        // Export each type as separate CSV
        Object.keys(exportData).forEach(key => {
          if (Array.isArray(exportData[key])) {
            exportToCSV(exportData[key], `${key}_${timestamp}.csv`);
          }
        });
        break;
      case 'excel':
        // Export each type as separate Excel file
        Object.keys(exportData).forEach(key => {
          if (Array.isArray(exportData[key])) {
            exportToExcel(exportData[key], `${key}_${timestamp}.xlsx`);
          }
        });
        break;
    }

    setIsExportDialogOpen(false);
    alert(`Export berhasil! Format: ${exportFormat.toUpperCase()}`);
  };

  // Import functionality
  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setImportFile(file);
    }
  };

  const handleImport = () => {
    if (!importFile) {
      alert('Pilih file terlebih dahulu!');
      return;
    }

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const content = e.target?.result as string;

        if (importFile.name.endsWith('.json')) {
          const data = JSON.parse(content);

          // Import alumni data
          if (data.alumni && Array.isArray(data.alumni)) {
            data.alumni.forEach((alumni: any) => {
              dataManager.addAlumni(alumni);
            });
          }

          // Import employment data
          if (data.employment && Array.isArray(data.employment)) {
            data.employment.forEach((emp: any) => {
              dataManager.addEmployment(emp);
            });
          }

          // Import survey data
          if (data.surveys && Array.isArray(data.surveys)) {
            data.surveys.forEach((survey: any) => {
              dataManager.addSurvey(survey);
            });
          }

          loadData();
          setIsImportDialogOpen(false);
          setImportFile(null);
          alert('Import berhasil!');

        } else if (importFile.name.endsWith('.csv')) {
          // Parse CSV
          const lines = content.split('\n');
          const headers = lines[0].split(',').map(h => h.replace(/"/g, '').trim());

          for (let i = 1; i < lines.length; i++) {
            if (lines[i].trim()) {
              const values = lines[i].split(',').map(v => v.replace(/"/g, '').trim());
              const record: any = {};

              headers.forEach((header, index) => {
                record[header] = values[index] || '';
              });

              // Determine data type based on headers and add to appropriate collection
              if (headers.includes('namaLengkap') || headers.includes('nim')) {
                dataManager.addAlumni(record);
              } else if (headers.includes('namaPerusahaan') || headers.includes('posisiJabatan')) {
                dataManager.addEmployment(record);
              }
            }
          }

          loadData();
          setIsImportDialogOpen(false);
          setImportFile(null);
          alert('Import CSV berhasil!');
        }

      } catch (error) {
        console.error('Import error:', error);
        alert('Error saat import file. Pastikan format file benar.');
      }
    };

    reader.readAsText(importFile);
  };

  return (
    <Layout>
      <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Laporan & Analytics</h1>
          <p className="text-gray-600">Analisis data alumni dan laporan komprehensif</p>
        </div>
        <div className="flex space-x-2">
          <Dialog open={isImportDialogOpen} onOpenChange={setIsImportDialogOpen}>
            <DialogTrigger asChild>
              <Button variant="outline">
                <Upload className="h-4 w-4 mr-2" />
                Import Data
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-md">
              <DialogHeader>
                <DialogTitle>Import Data</DialogTitle>
              </DialogHeader>
              <div className="space-y-4">
                <div>
                  <Label className="text-sm font-medium">Pilih File untuk Import:</Label>
                  <Input
                    type="file"
                    accept=".json,.csv,.xlsx,.xls"
                    onChange={(e) => setImportFile(e.target.files?.[0] || null)}
                    className="mt-1"
                  />
                </div>

                {importFile && (
                  <div className="p-3 bg-blue-50 rounded-lg">
                    <p className="text-sm text-blue-700">
                      File terpilih: {importFile.name}
                    </p>
                    <p className="text-xs text-blue-600 mt-1">
                      Ukuran: {(importFile.size / 1024).toFixed(1)} KB
                    </p>
                  </div>
                )}

                <div className="flex space-x-2">
                  <Button variant="outline" onClick={() => setIsImportDialogOpen(false)} className="flex-1">
                    Batal
                  </Button>
                  <Button onClick={handleImport} disabled={!importFile} className="flex-1 university-gradient">
                    Import
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>

          <Dialog open={isExportDialogOpen} onOpenChange={setIsExportDialogOpen}>
            <DialogTrigger asChild>
              <Button className="university-gradient">
                <Download className="h-4 w-4 mr-2" />
                Export Laporan
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-md">
              <DialogHeader>
                <DialogTitle>Export Laporan</DialogTitle>
              </DialogHeader>
              <div className="space-y-4">
                <div>
                  <Label className="text-sm font-medium">Pilih Data untuk Export:</Label>
                  <div className="space-y-2 mt-2">
                    {[
                      { id: 'alumni', label: 'Data Alumni' },
                      { id: 'employment', label: 'Data Employment' },
                      { id: 'surveys', label: 'Data Survey' },
                      { id: 'reports', label: 'Laporan Analisis' }
                    ].map((type) => (
                      <div key={type.id} className="flex items-center space-x-2">
                        <Checkbox
                          id={type.id}
                          checked={selectedExportTypes.includes(type.id)}
                          onCheckedChange={(checked) => {
                            if (checked) {
                              setSelectedExportTypes([...selectedExportTypes, type.id]);
                            } else {
                              setSelectedExportTypes(selectedExportTypes.filter(t => t !== type.id));
                            }
                          }}
                        />
                        <Label htmlFor={type.id} className="text-sm">{type.label}</Label>
                      </div>
                    ))}
                  </div>
                </div>

                <div>
                  <Label className="text-sm font-medium">Format Export:</Label>
                  <Select value={exportFormat} onValueChange={setExportFormat}>
                    <SelectTrigger className="mt-1">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="excel">Excel (.xlsx)</SelectItem>
                      <SelectItem value="csv">CSV (.csv)</SelectItem>
                      <SelectItem value="json">JSON (.json)</SelectItem>
                      <SelectItem value="pdf">PDF (.pdf)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex space-x-2">
                  <Button variant="outline" onClick={() => setIsExportDialogOpen(false)} className="flex-1">
                    Batal
                  </Button>
                  <Button onClick={handleBulkExport} className="flex-1 university-gradient">
                    Export
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>

          <Button variant="outline" onClick={() => exportReport('all-alumni')}>
            <FileText className="h-4 w-4 mr-2" />
            Quick Export
          </Button>
        </div>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex items-center space-x-2">
              <Filter className="h-4 w-4 text-gray-500" />
              <span className="text-sm font-medium">Filter:</span>
            </div>
            <Select value={selectedYear} onValueChange={setSelectedYear}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="Pilih Tahun Lulus" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Semua Tahun</SelectItem>
                {years.map(year => (
                  <SelectItem key={year} value={year.toString()}>{year}</SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Select value={selectedProgram} onValueChange={setSelectedProgram}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="Pilih Program Studi" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Semua Program</SelectItem>
                {programs.map(program => (
                  <SelectItem key={program} value={program}>{program}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Summary Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="h-8 w-8 flex items-center justify-center">
                <img
                  src="/logo-upi-manado.svg"
                  alt="Logo UPI Manado"
                  className="h-8 w-8"
                />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Alumni</p>
                <p className="text-2xl font-bold text-gray-900">{filteredAlumni.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <Users className="h-8 w-8 text-green-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Sudah Bekerja</p>
                <p className="text-2xl font-bold text-gray-900">
                  {employment.filter(e => e.statusPekerjaan === 'bekerja').length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <TrendingUp className="h-8 w-8 text-purple-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Rata-rata IPK</p>
                <p className="text-2xl font-bold text-gray-900">
                  {filteredAlumni.length > 0 
                    ? (filteredAlumni.reduce((sum, a) => sum + a.ipk, 0) / filteredAlumni.length).toFixed(2)
                    : '0.00'
                  }
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <BarChart3 className="h-8 w-8 text-orange-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Tingkat Kerja</p>
                <p className="text-2xl font-bold text-gray-900">
                  {employment.length > 0 
                    ? Math.round((employment.filter(e => e.statusPekerjaan === 'bekerja').length / employment.length) * 100)
                    : 0
                  }%
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Export Actions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
            <Button variant="outline" onClick={() => exportReport('all-alumni')} className="flex flex-col h-auto p-4">
              <Users className="h-6 w-6 mb-2 text-blue-600" />
              <span className="text-sm">Export Alumni</span>
              <span className="text-xs text-gray-500">{filteredAlumni.length} records</span>
            </Button>

            <Button variant="outline" onClick={() => exportReport('all-employment')} className="flex flex-col h-auto p-4">
              <Building className="h-6 w-6 mb-2 text-green-600" />
              <span className="text-sm">Export Pekerjaan</span>
              <span className="text-xs text-gray-500">{employment.length} records</span>
            </Button>

            <Button variant="outline" onClick={() => exportReport('all-surveys')} className="flex flex-col h-auto p-4">
              <FileText className="h-6 w-6 mb-2 text-purple-600" />
              <span className="text-sm">Export Survey</span>
              <span className="text-xs text-gray-500">{surveys.length} records</span>
            </Button>

            <Button variant="outline" onClick={() => {
              const timestamp = new Date().toISOString().split('T')[0];
              const allData = {
                alumni: filteredAlumni,
                employment: employment,
                surveys: surveys,
                summary: {
                  totalAlumni: filteredAlumni.length,
                  employedAlumni: employment.filter(e => e.statusPekerjaan === 'bekerja').length,
                  averageGPA: filteredAlumni.length > 0 ? (filteredAlumni.reduce((sum, a) => sum + a.ipk, 0) / filteredAlumni.length).toFixed(2) : '0.00',
                  employmentRate: employment.length > 0 ? Math.round((employment.filter(e => e.statusPekerjaan === 'bekerja').length / employment.length) * 100) : 0
                }
              };
              exportToJSON(allData, `complete_report_${timestamp}.json`);
            }} className="flex flex-col h-auto p-4">
              <Download className="h-6 w-6 mb-2 text-orange-600" />
              <span className="text-sm">Export Lengkap</span>
              <span className="text-xs text-gray-500">JSON Format</span>
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Alumni by Year */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <CardTitle>Alumni per Tahun Lulus</CardTitle>
            <Button variant="outline" size="sm" onClick={() => exportReport('alumni-by-year')}>
              <Download className="h-4 w-4" />
            </Button>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={alumniByYearData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="year" />
                <YAxis />
                <Tooltip />
                <Bar dataKey="count" fill="#3B82F6" />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Employment Status */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <CardTitle>Status Pekerjaan Alumni</CardTitle>
            <Button variant="outline" size="sm" onClick={() => exportReport('employment-status')}>
              <Download className="h-4 w-4" />
            </Button>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={employmentStatusData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {employmentStatusData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* IPK by Program */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <CardTitle>Rata-rata IPK per Program Studi</CardTitle>
            <Button variant="outline" size="sm" onClick={() => exportReport('ipk-by-program')}>
              <Download className="h-4 w-4" />
            </Button>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={ipkByProgramData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="program" />
                <YAxis domain={[0, 4]} />
                <Tooltip />
                <Bar dataKey="ipk" fill="#10B981" />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Salary Trend */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <CardTitle>Tren Gaji Alumni (dalam Juta)</CardTitle>
            <Button variant="outline" size="sm" onClick={() => exportReport('salary-trend')}>
              <Download className="h-4 w-4" />
            </Button>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={salaryTrendData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis />
                <Tooltip formatter={(value) => [`${value} Juta`, '']} />
                <Line type="monotone" dataKey="gajiPertama" stroke="#EF4444" name="Gaji Pertama" />
                <Line type="monotone" dataKey="gajiSaatIni" stroke="#10B981" name="Gaji Saat Ini" />
              </LineChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>
      </div>


    </Layout>
  );
};

export default Reports;
