import express from 'express';
import cors from 'cors';
import path from 'path';
import DatabaseConnection from './database/connection';

// Import routes
import alumniRoutes from './routes/alumni';
import employmentRoutes from './routes/employment';
import userRoutes from './routes/users';
import surveyRoutes from './routes/surveys';
import settingsRoutes from './routes/settings';
import migrationRoutes from './routes/migration';

const app = express();
const PORT = process.env.PORT || 3002;

// Middleware
app.use(cors({
  origin: ['http://localhost:8081', 'http://localhost:3000', 'http://localhost:5173'],
  credentials: true
}));
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Initialize database connection
const dbConnection = DatabaseConnection.getInstance();

// Health check endpoint
app.get('/api/health', (req, res) => {
  try {
    const stats = dbConnection.getStats();
    res.json({
      success: true,
      message: 'Server is running',
      database: 'SQLite',
      stats
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Database connection failed',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// API Routes
app.use('/api/alumni', alumniRoutes);
app.use('/api/employment', employmentRoutes);
app.use('/api/users', userRoutes);
app.use('/api/surveys', surveyRoutes);
app.use('/api/settings', settingsRoutes);
app.use('/api/migration', migrationRoutes);

// Error handling middleware
app.use((err: any, req: express.Request, res: express.Response, next: express.NextFunction) => {
  console.error('Error:', err);
  
  if (err.code === 'SQLITE_CONSTRAINT_UNIQUE') {
    return res.status(400).json({
      success: false,
      message: 'Data already exists',
      error: 'Duplicate entry'
    });
  }
  
  if (err.code === 'SQLITE_CONSTRAINT_FOREIGNKEY') {
    return res.status(400).json({
      success: false,
      message: 'Invalid reference',
      error: 'Foreign key constraint failed'
    });
  }

  res.status(500).json({
    success: false,
    message: 'Internal server error',
    error: process.env.NODE_ENV === 'development' ? err.message : 'Something went wrong'
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: 'Endpoint not found'
  });
});

// Graceful shutdown
process.on('SIGINT', () => {
  console.log('Shutting down server...');
  dbConnection.close();
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('Shutting down server...');
  dbConnection.close();
  process.exit(0);
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 Server running on http://localhost:${PORT}`);
  console.log(`📊 Database: SQLite`);
  console.log(`🔗 API Base URL: http://localhost:${PORT}/api`);
  
  // Log database stats
  try {
    const stats = dbConnection.getStats();
    console.log('📈 Database Stats:', stats);
  } catch (error) {
    console.error('❌ Database connection error:', error);
  }
});

export default app;
