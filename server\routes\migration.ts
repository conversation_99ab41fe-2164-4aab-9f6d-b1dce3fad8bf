import express from 'express';
import AlumniModel from '../models/AlumniModel';
import EmploymentModel from '../models/EmploymentModel';
import UserModel from '../models/UserModel';
import DatabaseConnection from '../database/connection';

const router = express.Router();
const alumniModel = new AlumniModel();
const employmentModel = new EmploymentModel();
const userModel = new UserModel();
const db = DatabaseConnection.getInstance().getDatabase();

// POST /api/migration/from-json - Migrate data from JSON format
router.post('/from-json', (req, res) => {
  try {
    const { alumni, employment, users, surveys, settings, survey_responses } = req.body;
    
    let results = {
      alumni: 0,
      employment: 0,
      users: 0,
      surveys: 0,
      settings: 0,
      survey_responses: 0,
      errors: [] as string[]
    };

    // Start transaction
    const migrate = db.transaction(() => {
      // Migrate Alumni
      if (alumni && Array.isArray(alumni)) {
        try {
          const alumniData = alumni.map((item: any) => ({
            user_id: item.userId,
            nim: item.nim,
            nama_lengkap: item.namaLengkap,
            program_studi: item.programStudi,
            fakultas: item.fakultas,
            tahun_masuk: item.tahunMasuk,
            tahun_lulus: item.tahunLulus,
            ipk: item.ipk,
            email: item.email,
            no_telepon: item.noTelepon,
            alamat: item.alamat,
            status_verifikasi: item.statusVerifikasi || 'pending'
          }));
          
          results.alumni = alumniModel.bulkInsert(alumniData);
        } catch (error) {
          results.errors.push(`Alumni migration error: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
      }

      // Migrate Employment
      if (employment && Array.isArray(employment)) {
        try {
          const employmentData = employment.map((item: any) => ({
            alumni_id: item.alumniId,
            nama_perusahaan: item.namaPerusahaan,
            posisi_jabatan: item.posisiJabatan,
            jenis_usaha: item.jenisUsaha,
            gaji_pertama: item.gajiPertama,
            gaji_saat_ini: item.gajiSaatIni,
            tanggal_mulai_kerja: item.tanggalMulaiKerja,
            status_pekerjaan: item.statusPekerjaan || 'bekerja',
            relevansi_pekerjaan: item.relevansiPekerjaan
          }));
          
          results.employment = employmentModel.bulkInsert(employmentData);
        } catch (error) {
          results.errors.push(`Employment migration error: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
      }

      // Migrate Users
      if (users && Array.isArray(users)) {
        try {
          const userData = users.map((item: any) => ({
            username: item.username,
            email: item.email,
            role: item.role || 'alumni',
            nama_lengkap: item.namaLengkap
          }));
          
          results.users = userModel.bulkInsert(userData);
        } catch (error) {
          results.errors.push(`Users migration error: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
      }

      // Migrate Surveys
      if (surveys && Array.isArray(surveys)) {
        try {
          const stmt = db.prepare(`
            INSERT INTO surveys (
              id, judul, deskripsi, tanggal_mulai, tanggal_selesai,
              status, target_alumni, questions, created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
          `);

          surveys.forEach((item: any) => {
            const now = new Date().toISOString();
            stmt.run(
              item.id || `SRV${Date.now()}${Math.random().toString(36).substr(2, 5)}`,
              item.judul,
              item.deskripsi || null,
              item.tanggalMulai || null,
              item.tanggalSelesai || null,
              item.status || 'active',
              item.targetAlumni ? JSON.stringify(item.targetAlumni) : null,
              item.questions ? JSON.stringify(item.questions) : null,
              item.createdAt || now,
              item.updatedAt || now
            );
            results.surveys++;
          });
        } catch (error) {
          results.errors.push(`Surveys migration error: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
      }

      // Migrate Survey Responses
      if (survey_responses && Array.isArray(survey_responses)) {
        try {
          const stmt = db.prepare(`
            INSERT INTO survey_responses (id, survey_id, alumni_id, responses, submitted_at)
            VALUES (?, ?, ?, ?, ?)
          `);

          survey_responses.forEach((item: any) => {
            stmt.run(
              item.id || `RESP${Date.now()}${Math.random().toString(36).substr(2, 5)}`,
              item.surveyId,
              item.alumniId,
              JSON.stringify(item.responses),
              item.submittedAt || new Date().toISOString()
            );
            results.survey_responses++;
          });
        } catch (error) {
          results.errors.push(`Survey responses migration error: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
      }

      // Migrate Settings
      if (settings && Array.isArray(settings)) {
        try {
          const stmt = db.prepare(`
            INSERT OR REPLACE INTO settings (id, key, value, description, updated_at)
            VALUES (?, ?, ?, ?, ?)
          `);

          settings.forEach((item: any) => {
            stmt.run(
              item.id || `SET${Date.now()}${Math.random().toString(36).substr(2, 5)}`,
              item.key,
              JSON.stringify(item.value),
              item.description || '',
              item.updatedAt || new Date().toISOString()
            );
            results.settings++;
          });
        } catch (error) {
          results.errors.push(`Settings migration error: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
      }
    });

    // Execute migration
    migrate();

    res.json({
      success: true,
      message: 'Data migration completed',
      data: results
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Migration failed',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// GET /api/migration/export - Export current SQLite data to JSON format
router.get('/export', (req, res) => {
  try {
    const data = {
      alumni: db.prepare('SELECT * FROM alumni').all(),
      employment: db.prepare('SELECT * FROM employment').all(),
      users: db.prepare('SELECT * FROM users').all(),
      surveys: db.prepare('SELECT * FROM surveys').all().map((survey: any) => ({
        ...survey,
        target_alumni: survey.target_alumni ? JSON.parse(survey.target_alumni) : [],
        questions: survey.questions ? JSON.parse(survey.questions) : []
      })),
      survey_responses: db.prepare('SELECT * FROM survey_responses').all().map((response: any) => ({
        ...response,
        responses: JSON.parse(response.responses)
      })),
      settings: db.prepare('SELECT * FROM settings').all().map((setting: any) => ({
        ...setting,
        value: JSON.parse(setting.value)
      })),
      metadata: {
        exported_at: new Date().toISOString(),
        database_type: 'SQLite',
        version: '1.0.0'
      }
    };

    res.json({
      success: true,
      data
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Export failed',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// POST /api/migration/clear - Clear all data (for testing)
router.post('/clear', (req, res) => {
  try {
    const { confirm } = req.body;
    
    if (confirm !== 'CLEAR_ALL_DATA') {
      return res.status(400).json({
        success: false,
        message: 'Confirmation required. Send { "confirm": "CLEAR_ALL_DATA" }'
      });
    }

    // Clear all tables
    const clearTables = db.transaction(() => {
      db.prepare('DELETE FROM survey_responses').run();
      db.prepare('DELETE FROM employment').run();
      db.prepare('DELETE FROM surveys').run();
      db.prepare('DELETE FROM alumni').run();
      db.prepare('DELETE FROM users WHERE id != "admin-001"').run(); // Keep admin user
      db.prepare('DELETE FROM settings').run();
      db.prepare('DELETE FROM reports').run();
    });

    clearTables();

    res.json({
      success: true,
      message: 'All data cleared successfully'
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Failed to clear data',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// GET /api/migration/status - Get migration status and database info
router.get('/status', (req, res) => {
  try {
    const stats = DatabaseConnection.getInstance().getStats();
    
    res.json({
      success: true,
      data: {
        database_type: 'SQLite',
        database_path: 'data/tracer_alumni.db',
        tables: stats,
        total_records: Object.values(stats).reduce((sum: number, table: any) => sum + table.count, 0),
        last_check: new Date().toISOString()
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Failed to get migration status',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

export default router;
