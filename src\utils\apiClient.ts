// API Client for SQLite backend

const API_BASE_URL = 'http://localhost:3001/api';

export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
  pagination?: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };
}

class ApiClient {
  private baseUrl: string;

  constructor(baseUrl: string = API_BASE_URL) {
    this.baseUrl = baseUrl;
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    const url = `${this.baseUrl}${endpoint}`;
    
    const defaultOptions: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
      },
    };

    const config = { ...defaultOptions, ...options };

    try {
      const response = await fetch(url, config);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || `HTTP error! status: ${response.status}`);
      }

      return data;
    } catch (error) {
      console.error(`API request failed: ${endpoint}`, error);
      throw error;
    }
  }

  // Alumni API methods
  async getAlumni(params?: {
    page?: number;
    limit?: number;
    status?: string;
    program_studi?: string;
    fakultas?: string;
    tahun_lulus?: number;
    search?: string;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
  }) {
    const queryParams = new URLSearchParams();
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined) {
          queryParams.append(key, value.toString());
        }
      });
    }
    
    const endpoint = `/alumni${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
    return this.request(endpoint);
  }

  async getAlumniById(id: string) {
    return this.request(`/alumni/${id}`);
  }

  async getAlumniByNim(nim: string) {
    return this.request(`/alumni/nim/${nim}`);
  }

  async createAlumni(data: any) {
    return this.request('/alumni', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async updateAlumni(id: string, data: any) {
    return this.request(`/alumni/${id}`, {
      method: 'PUT',
      body: JSON.stringify(data),
    });
  }

  async deleteAlumni(id: string) {
    return this.request(`/alumni/${id}`, {
      method: 'DELETE',
    });
  }

  async getAlumniStatistics() {
    return this.request('/alumni/statistics');
  }

  async bulkInsertAlumni(alumni: any[]) {
    return this.request('/alumni/bulk', {
      method: 'POST',
      body: JSON.stringify(alumni),
    });
  }

  // Employment API methods
  async getEmployment() {
    return this.request('/employment');
  }

  async getEmploymentById(id: string) {
    return this.request(`/employment/${id}`);
  }

  async getEmploymentByAlumniId(alumniId: string) {
    return this.request(`/employment/alumni/${alumniId}`);
  }

  async createEmployment(data: any) {
    return this.request('/employment', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async updateEmployment(id: string, data: any) {
    return this.request(`/employment/${id}`, {
      method: 'PUT',
      body: JSON.stringify(data),
    });
  }

  async deleteEmployment(id: string) {
    return this.request(`/employment/${id}`, {
      method: 'DELETE',
    });
  }

  async getEmploymentStatistics() {
    return this.request('/employment/statistics');
  }

  // Users API methods
  async getUsers() {
    return this.request('/users');
  }

  async getUserById(id: string) {
    return this.request(`/users/${id}`);
  }

  async createUser(data: any) {
    return this.request('/users', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async updateUser(id: string, data: any) {
    return this.request(`/users/${id}`, {
      method: 'PUT',
      body: JSON.stringify(data),
    });
  }

  async deleteUser(id: string) {
    return this.request(`/users/${id}`, {
      method: 'DELETE',
    });
  }

  async authenticateUser(email: string, password: string) {
    return this.request('/users/authenticate', {
      method: 'POST',
      body: JSON.stringify({ email, password }),
    });
  }

  // Surveys API methods
  async getSurveys() {
    return this.request('/surveys');
  }

  async getSurveyById(id: string) {
    return this.request(`/surveys/${id}`);
  }

  async createSurvey(data: any) {
    return this.request('/surveys', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async updateSurvey(id: string, data: any) {
    return this.request(`/surveys/${id}`, {
      method: 'PUT',
      body: JSON.stringify(data),
    });
  }

  async deleteSurvey(id: string) {
    return this.request(`/surveys/${id}`, {
      method: 'DELETE',
    });
  }

  async getSurveyResponses(surveyId: string) {
    return this.request(`/surveys/${surveyId}/responses`);
  }

  async submitSurveyResponse(surveyId: string, data: any) {
    return this.request(`/surveys/${surveyId}/responses`, {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  // Settings API methods
  async getSettings() {
    return this.request('/settings');
  }

  async getSetting(key: string) {
    return this.request(`/settings/${key}`);
  }

  async updateSetting(key: string, value: any, description?: string) {
    return this.request(`/settings/${key}`, {
      method: 'PUT',
      body: JSON.stringify({ value, description }),
    });
  }

  async bulkUpdateSettings(settings: { [key: string]: any }) {
    return this.request('/settings/bulk', {
      method: 'POST',
      body: JSON.stringify(settings),
    });
  }

  async deleteSetting(key: string) {
    return this.request(`/settings/${key}`, {
      method: 'DELETE',
    });
  }

  async resetSettings() {
    return this.request('/settings/reset', {
      method: 'POST',
    });
  }

  // Migration API methods
  async migrateFromJson(data: any) {
    return this.request('/migration/from-json', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async exportData() {
    return this.request('/migration/export');
  }

  async clearAllData() {
    return this.request('/migration/clear', {
      method: 'POST',
      body: JSON.stringify({ confirm: 'CLEAR_ALL_DATA' }),
    });
  }

  async getMigrationStatus() {
    return this.request('/migration/status');
  }

  // Health check
  async healthCheck() {
    return this.request('/health');
  }
}

export const apiClient = new ApiClient();
export default apiClient;
